import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';

import { PersonalDataInventory, DataClassification } from '../entities/personal-data-inventory.entity';
import { DataProcessingLog, ProcessingActivity, ProcessingPurpose, ProcessingResult } from '../entities/data-processing-log.entity';

export interface DataProtectionConfig {
  encryptionKey: string;
  encryptionAlgorithm: string;
  hashAlgorithm: string;
  saltRounds: number;
  dataMinimizationEnabled: boolean;
  pseudonymizationEnabled: boolean;
}

export interface ProtectionResult {
  success: boolean;
  protectedData?: any;
  error?: string;
  metadata?: Record<string, any>;
}

@Injectable()
export class DataProtectionService {
  private readonly logger = new Logger(DataProtectionService.name);
  private readonly config: DataProtectionConfig;

  constructor(
    @InjectRepository(PersonalDataInventory)
    private readonly dataInventoryRepository: Repository<PersonalDataInventory>,
    @InjectRepository(DataProcessingLog)
    private readonly processingLogRepository: Repository<DataProcessingLog>,
    private readonly configService: ConfigService,
  ) {
    this.config = {
      encryptionKey: this.configService.get<string>('DATA_ENCRYPTION_KEY') || this.generateEncryptionKey(),
      encryptionAlgorithm: this.configService.get<string>('DATA_ENCRYPTION_ALGORITHM') || 'aes-256-gcm',
      hashAlgorithm: this.configService.get<string>('DATA_HASH_ALGORITHM') || 'sha256',
      saltRounds: this.configService.get<number>('DATA_SALT_ROUNDS') || 12,
      dataMinimizationEnabled: this.configService.get<boolean>('DATA_MINIMIZATION_ENABLED') || true,
      pseudonymizationEnabled: this.configService.get<boolean>('PSEUDONYMIZATION_ENABLED') || true,
    };
  }

  private generateEncryptionKey(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  async encryptData(data: any, classification: DataClassification): Promise<ProtectionResult> {
    try {
      if (!this.requiresEncryption(classification)) {
        return { success: true, protectedData: data };
      }

      const serializedData = JSON.stringify(data);
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipheriv(
        this.config.encryptionAlgorithm,
        Buffer.from(this.config.encryptionKey, 'hex').slice(0, 32),
        iv
      );
      
      let encrypted = cipher.update(serializedData, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      const authTag = cipher.getAuthTag?.() || Buffer.alloc(0);
      
      const encryptedData = {
        data: encrypted,
        iv: iv.toString('hex'),
        authTag: authTag.toString('hex'),
        algorithm: this.config.encryptionAlgorithm,
        timestamp: new Date().toISOString(),
      };

      this.logger.log(`Data encrypted successfully with classification: ${classification}`);
      
      return {
        success: true,
        protectedData: encryptedData,
        metadata: {
          classification,
          encrypted: true,
          algorithm: this.config.encryptionAlgorithm,
        },
      };
    } catch (error) {
      this.logger.error('Failed to encrypt data:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  async decryptData(encryptedData: any): Promise<ProtectionResult> {
    try {
      if (!encryptedData || !encryptedData.data) {
        return { success: true, protectedData: encryptedData };
      }

      const decipher = crypto.createDecipheriv(
        encryptedData.algorithm || this.config.encryptionAlgorithm,
        Buffer.from(this.config.encryptionKey, 'hex').slice(0, 32),
        Buffer.from(encryptedData.iv, 'hex')
      );
      
      if (encryptedData.authTag) {
        decipher.setAuthTag(Buffer.from(encryptedData.authTag, 'hex'));
      }
      
      let decrypted = decipher.update(encryptedData.data, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      const data = JSON.parse(decrypted);

      this.logger.log('Data decrypted successfully');
      
      return {
        success: true,
        protectedData: data,
        metadata: {
          decrypted: true,
          originalTimestamp: encryptedData.timestamp,
        },
      };
    } catch (error) {
      this.logger.error('Failed to decrypt data:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  async hashData(data: string, addSalt: boolean = true): Promise<string> {
    const salt = addSalt ? crypto.randomBytes(16).toString('hex') : '';
    const hash = crypto.createHash(this.config.hashAlgorithm);
    hash.update(data + salt);
    return hash.digest('hex') + (addSalt ? ':' + salt : '');
  }

  async verifyHash(data: string, hashedData: string): Promise<boolean> {
    try {
      const [hash, salt] = hashedData.split(':');
      const verifyHash = crypto.createHash(this.config.hashAlgorithm);
      verifyHash.update(data + (salt || ''));
      return verifyHash.digest('hex') === hash;
    } catch (error) {
      this.logger.error('Failed to verify hash:', error);
      return false;
    }
  }

  async pseudonymizeData(data: any, userId: string): Promise<ProtectionResult> {
    try {
      if (!this.config.pseudonymizationEnabled) {
        return { success: true, protectedData: data };
      }

      const pseudonymizedData = { ...data };
      const pseudonymId = await this.generatePseudonymId(userId);
      
      // Replace identifying information with pseudonym
      if (pseudonymizedData.id) {
        pseudonymizedData.id = pseudonymId;
      }
      if (pseudonymizedData.userId) {
        pseudonymizedData.userId = pseudonymId;
      }
      if (pseudonymizedData.email) {
        pseudonymizedData.email = `${pseudonymId}@pseudonym.local`;
      }
      if (pseudonymizedData.name) {
        pseudonymizedData.name = `Pseudonym-${pseudonymId.slice(0, 8)}`;
      }

      this.logger.log(`Data pseudonymized for user ${userId}`);
      
      return {
        success: true,
        protectedData: pseudonymizedData,
        metadata: {
          pseudonymized: true,
          originalUserId: userId,
          pseudonymId,
        },
      };
    } catch (error) {
      this.logger.error('Failed to pseudonymize data:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  async generatePseudonymId(userId: string): Promise<string> {
    const hash = crypto.createHash(this.config.hashAlgorithm);
    hash.update(userId + this.config.encryptionKey);
    return hash.digest('hex').slice(0, 16);
  }

  async applyDataMinimization(data: any, purpose: string): Promise<ProtectionResult> {
    try {
      if (!this.config.dataMinimizationEnabled) {
        return { success: true, protectedData: data };
      }

      const minimizedData = await this.minimizeDataForPurpose(data, purpose);
      
      this.logger.log(`Data minimized for purpose: ${purpose}`);
      
      return {
        success: true,
        protectedData: minimizedData,
        metadata: {
          minimized: true,
          purpose,
          originalFields: Object.keys(data).length,
          minimizedFields: Object.keys(minimizedData).length,
        },
      };
    } catch (error) {
      this.logger.error('Failed to apply data minimization:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  private async minimizeDataForPurpose(data: any, purpose: string): Promise<any> {
    const minimizedData: any = {};
    
    // Define field mappings for different purposes
    const purposeFieldMappings: Record<string, string[]> = {
      'authentication': ['id', 'email', 'password', 'lastLogin'],
      'marketing': ['id', 'email', 'name', 'preferences'],
      'analytics': ['id', 'createdAt', 'lastActivity', 'userAgent'],
      'support': ['id', 'email', 'name', 'supportHistory'],
      'billing': ['id', 'email', 'name', 'billingAddress', 'paymentMethod'],
      'legal': ['id', 'email', 'name', 'legalDocuments', 'consentRecords'],
    };

    const allowedFields = purposeFieldMappings[purpose] || Object.keys(data);
    
    // Copy only allowed fields
    for (const field of allowedFields) {
      if (data.hasOwnProperty(field)) {
        minimizedData[field] = data[field];
      }
    }

    return minimizedData;
  }

  async protectSensitiveData(data: any, classification: DataClassification): Promise<ProtectionResult> {
    try {
      let protectedData = data;
      const metadata: Record<string, any> = {
        classification,
        protectionMeasures: [],
      };

      // Apply encryption if required
      if (this.requiresEncryption(classification)) {
        const encryptionResult = await this.encryptData(protectedData, classification);
        if (!encryptionResult.success) {
          return encryptionResult;
        }
        protectedData = encryptionResult.protectedData;
        metadata.protectionMeasures.push('encryption');
      }

      // Apply pseudonymization for special categories
      if (classification === DataClassification.SPECIAL_CATEGORY) {
        const pseudonymResult = await this.pseudonymizeData(protectedData, 'system');
        if (!pseudonymResult.success) {
          return pseudonymResult;
        }
        protectedData = pseudonymResult.protectedData;
        metadata.protectionMeasures.push('pseudonymization');
      }

      this.logger.log(`Data protected with classification: ${classification}`);
      
      return {
        success: true,
        protectedData,
        metadata,
      };
    } catch (error) {
      this.logger.error('Failed to protect sensitive data:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  private requiresEncryption(classification: DataClassification): boolean {
    return [
      DataClassification.CONFIDENTIAL,
      DataClassification.RESTRICTED,
      DataClassification.SPECIAL_CATEGORY,
    ].includes(classification);
  }

  async validateDataAccess(userId: string, dataElement: PersonalDataInventory): Promise<boolean> {
    try {
      // Check if user has access to this data element
      const accessControls = dataElement.accessControls || {};
      
      // Check role-based access
      if (accessControls.requiredRoles && Array.isArray(accessControls.requiredRoles)) {
        // This would need to be implemented with actual user role checking
        // For now, we'll assume basic access
      }

      // Check time-based access
      if (accessControls.timeRestrictions) {
        const now = new Date();
        const allowedHours = accessControls.timeRestrictions.allowedHours || [];
        const currentHour = now.getHours();
        
        if (allowedHours.length > 0 && !allowedHours.includes(currentHour)) {
          return false;
        }
      }

      // Check IP-based access
      if (accessControls.ipRestrictions) {
        // This would need to be implemented with actual IP checking
        // For now, we'll assume basic access
      }

      // Log access attempt
      await this.processingLogRepository.save(this.processingLogRepository.create({
        userId,
        dataSubject: dataElement.dataElementName,
        activity: ProcessingActivity.ACCESS,
        processingPurpose: ProcessingPurpose.SYSTEM_ADMINISTRATION,
        legalBasis: 'legitimate_interests',
        dataCategories: dataElement.dataCategory,
        systemComponent: 'data-protection-service',
        result: ProcessingResult.SUCCESS,
        isAutomated: false,
        hasUserConsent: false,
        involvesSpecialCategories: false,
        timestamp: new Date(),
      }));

      return true;
    } catch (error) {
      this.logger.error('Failed to validate data access:', error);
      return false;
    }
  }

  async auditDataProtection(): Promise<any> {
    this.logger.log('Starting data protection audit');

    const dataElements = await this.dataInventoryRepository.find({
      where: { isActive: true },
    });

    const audit = {
      totalDataElements: dataElements.length,
      encryptedElements: 0,
      pseudonymizedElements: 0,
      elementsWithAccessControls: 0,
      elementsWithSecurityMeasures: 0,
      complianceScore: 0,
      recommendations: [] as string[],
      details: [] as any[],
    };

    for (const element of dataElements) {
      const elementAudit = {
        id: element.id,
        name: element.dataElementName,
        classification: element.classification,
        isEncrypted: element.isEncrypted,
        isPseudonymized: element.isPseudonymized,
        hasAccessControls: !!(element.accessControls && Object.keys(element.accessControls).length > 0),
        hasSecurityMeasures: !!(element.securityMeasures && Object.keys(element.securityMeasures).length > 0),
        complianceScore: element.getComplianceScore(),
        issues: [] as string[],
      };

      // Check encryption requirements
      if (this.requiresEncryption(element.classification) && !element.isEncrypted) {
        elementAudit.issues.push('Encryption required but not applied');
      } else if (element.isEncrypted) {
        audit.encryptedElements++;
      }

      // Check pseudonymization
      if (element.isPseudonymized) {
        audit.pseudonymizedElements++;
      }

      // Check access controls
      if (elementAudit.hasAccessControls) {
        audit.elementsWithAccessControls++;
      } else if (element.classification !== DataClassification.PUBLIC) {
        elementAudit.issues.push('Access controls not defined');
      }

      // Check security measures
      if (elementAudit.hasSecurityMeasures) {
        audit.elementsWithSecurityMeasures++;
      } else if (element.requiresSpecialProtection()) {
        elementAudit.issues.push('Security measures not defined');
      }

      audit.details.push(elementAudit);
    }

    // Calculate overall compliance score
    audit.complianceScore = audit.details.length > 0
      ? Math.round(audit.details.reduce((sum, detail) => sum + detail.complianceScore, 0) / audit.details.length)
      : 100;

    // Generate recommendations
    if (audit.complianceScore < 80) {
      audit.recommendations.push('Improve overall data protection measures');
    }
    if (audit.elementsWithAccessControls < audit.totalDataElements * 0.9) {
      audit.recommendations.push('Implement access controls for all non-public data elements');
    }
    if (audit.encryptedElements < audit.totalDataElements * 0.5) {
      audit.recommendations.push('Increase encryption coverage for sensitive data');
    }

    this.logger.log(`Data protection audit completed with score: ${audit.complianceScore}%`);
    return audit;
  }

  async generateDataProtectionReport(): Promise<any> {
    const audit = await this.auditDataProtection();
    
    return {
      auditDate: new Date(),
      summary: {
        totalDataElements: audit.totalDataElements,
        complianceScore: audit.complianceScore,
        encryptionCoverage: audit.totalDataElements > 0 ? (audit.encryptedElements / audit.totalDataElements) * 100 : 0,
        accessControlCoverage: audit.totalDataElements > 0 ? (audit.elementsWithAccessControls / audit.totalDataElements) * 100 : 0,
      },
      recommendations: audit.recommendations,
      details: audit.details,
      nextAuditDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days from now
    };
  }
}